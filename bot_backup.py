import os
import sys
import logging
import signal
import time
import atexit
import psutil
import asyncio
from pathlib import Path
from telegram import Update
from telegram.ext import (
    Application, CommandHandler, CallbackQueryHandler,
    ChatJoinRequestHandler, ContextTypes, ChatMemberHandler,
    MessageHandler, filters
)
from config import BOT_TOKEN, MAIN_CHANNEL, MAIN_CHANNEL_ID, SECOND_CHANNEL, SUPPORT_BOT, BOT_USERNAME, MONGODB_URI
from handlers import (
    start, help_command, mychannel_command, check_joined_callback,
    start_process_callback, mychannel_callback, premium_callback,
    refresh_mychannel_callback, choose_payment_callback, i_have_joined_callback,
    premium_command, join_request_handler, handle_chat_member_update,
    show_payment_methods_callback, crypto_payment_callback, stats_command,
    scan_command, scan_channel_callback, scan_menu_callback
)
from admin import get_admin_handlers
from database import add_user, channels_collection
from utils import setup_logger
from forward import register_handlers, start_background_tasks
from telegram.error import TimedOut, NetworkError

# Create a custom filter to exclude "hasn't started a conversation" logs and similar messages
class HasStartedFilter(logging.Filter):
    def filter(self, record):
        message = record.getMessage()
        # Filter out all variations of "user hasn't started" messages
        if "hasn't started a conversation" in message:
            return False
        if "User hasn't started the bot" in message:
            return False
        if "Cannot send welcome message to user" in message and "User hasn't started the bot" in message:
            return False
        return True

# Configure logging
logger = logging.getLogger("bot")
setup_logger()

# Apply the custom filter
for handler in logger.handlers:
    handler.addFilter(HasStartedFilter())

# Global application
application = None

def get_pid_from_lock_file():
    """Read the PID from the lock file if it exists."""
    try:
        with open("bot.lock", "r") as f:
            return int(f.read().strip())
    except (ValueError, IOError, FileNotFoundError):
        return None

def check_lock_file():
    """Check if a lock file exists and if the process is still running."""
    # Check if bot.lock exists
    if os.path.exists("bot.lock"):
        pid = get_pid_from_lock_file()
        if pid is None:
            # Invalid PID in lock file, remove it
            logger.warning("Invalid lock file found. Removing it.")
            try:
                os.unlink("bot.lock")
            except (IOError, PermissionError) as e:
                # On Windows, sometimes file operations can fail due to timing issues
                logger.error(f"Error removing lock file: {e}")
                for i in range(3):  # Try 3 times with a delay
                    time.sleep(0.5)
                    try:
                        os.unlink("bot.lock")
                        break
                    except (IOError, PermissionError):
                        pass
        else:
            # Check if process is running
            try:
                # On Windows, this will raise if process doesn't exist or has no permissions
                # On Unix, this will not raise but rather return an empty list
                process = psutil.Process(pid)
                if process.is_running():
                    logger.error(f"Bot is already running with PID {pid}")
                    sys.exit(1)
                else:
                    logger.warning(f"Process {pid} not running. Removing stale lock file.")
                    try:
                        os.unlink("bot.lock")
                    except (IOError, PermissionError) as e:
                        logger.error(f"Error removing stale lock file: {e}")
                        for i in range(3):  # Try 3 times with a delay
                            time.sleep(0.5)
                            try:
                                os.unlink("bot.lock")
                                break
                            except (IOError, PermissionError):
                                pass
            except psutil.NoSuchProcess:
                logger.warning(f"Process {pid} does not exist. Removing stale lock file.")
                try:
                    os.unlink("bot.lock")
                except (IOError, PermissionError) as e:
                    logger.error(f"Error removing stale lock file: {e}")
                    for i in range(3):  # Try 3 times with a delay
                        time.sleep(0.5)
                        try:
                            os.unlink("bot.lock")
                            break
                        except (IOError, PermissionError):
                            pass

    # Create new lock file
    with open("bot.lock", "w") as f:
        f.write(str(os.getpid()))
    logger.info(f"Created lock file with PID {os.getpid()}")

def remove_lock_file():
    """Remove the lock file if it belongs to the current process."""
    if os.path.exists("bot.lock"):
        pid = get_pid_from_lock_file()
        if pid == os.getpid():
            try:
                os.unlink("bot.lock")
                logger.info("Removed lock file")
            except (IOError, PermissionError) as e:
                logger.error(f"Error removing lock file during shutdown: {e}")
                # Try again with a delay for Windows systems
                try:
                    time.sleep(0.5)
                    os.unlink("bot.lock")
                    logger.info("Removed lock file after retry")
                except (IOError, PermissionError) as e2:
                    logger.error(f"Failed to remove lock file even after retry: {e2}")

def signal_handler(sig, frame):
    """Handle SIGINT and SIGTERM signals."""
    logger.info(f"Received signal {sig}, shutting down...")
    if application:
        # Only stop if application exists and is running
        asyncio.create_task(application.stop())
    remove_lock_file()
    sys.exit(0)

async def post_init(application: Application):
    """Post-initialization actions after the bot is running."""
    logger.info("Bot started successfully. Initializing background tasks...")
    # Start background tasks once the bot is running
    start_background_tasks(application)
    logger.info("Background tasks initialized.")

def main():
    """Start the bot."""
    global application

    try:
        # Register signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        # Check for running instances
        check_lock_file()

        # Create the Application and pass it your bot's token
        application = Application.builder().token(BOT_TOKEN).post_init(post_init).build()

        # Command handlers
        application.add_handler(CommandHandler("start", start))
        application.add_handler(CommandHandler("help", help_command))
        application.add_handler(CommandHandler("mychannel", mychannel_command))
        application.add_handler(CommandHandler("premium", premium_command))
        application.add_handler(CommandHandler("scan", scan_command))
        application.add_handler(CommandHandler("stats", stats_command))

        # Register admin handlers - these will be registered with lower priority
        try:
            from admin import register_admin_handlers
            register_admin_handlers(application)
            logger.info("Admin command handlers registered successfully")
        except Exception as e:
            logger.error(f"Error registering admin handlers: {e}")

        # Callback query handlers
        application.add_handler(CallbackQueryHandler(start_process_callback, pattern="^start_process$"))
        application.add_handler(CallbackQueryHandler(i_have_joined_callback, pattern="^i_have_joined$"))
        application.add_handler(CallbackQueryHandler(premium_callback, pattern="^premium$"))
        application.add_handler(CallbackQueryHandler(refresh_mychannel_callback, pattern="^refresh_channels$"))
        application.add_handler(CallbackQueryHandler(choose_payment_callback, pattern="^choose_payment$"))
        application.add_handler(CallbackQueryHandler(show_payment_methods_callback, pattern="^show_payment_methods$"))
        application.add_handler(CallbackQueryHandler(crypto_payment_callback, pattern="^crypto_payment$"))
        application.add_handler(CallbackQueryHandler(check_joined_callback, pattern="^check_joined$"))
        application.add_handler(CallbackQueryHandler(mychannel_callback, pattern="^mychannel$"))
        application.add_handler(CallbackQueryHandler(help_command, pattern="^help_command$"))

        # Scan-related handlers
        application.add_handler(CallbackQueryHandler(scan_menu_callback, pattern="^scan_menu$"))
        application.add_handler(CallbackQueryHandler(scan_channel_callback, pattern="^scan_channel_"))

        # Join request and chat member handlers
        application.add_handler(ChatJoinRequestHandler(join_request_handler))
        application.add_handler(ChatMemberHandler(handle_chat_member_update, ChatMemberHandler.MY_CHAT_MEMBER))

        # Register the forwarding handlers silently
        try:
            import forward
            forward.register_handlers(application)
            logger.info("Media forwarding module registered")
        except Exception as e:
            logger.error(f"Error registering media forwarding module: {e}")

        # Error handler
        application.add_error_handler(error_handler)

        # Start the Bot
        logger.info("Bot starting...")
        application.run_polling(allowed_updates=Update.ALL_TYPES)
    except Exception as e:
        logger.error(f"Error starting bot: {e}")
    finally:
        # Clean up lock file
        remove_lock_file()

async def error_handler(update, context):
    """Log errors caused by updates."""
    try:
        error = context.error
        error_msg = str(error)

        # Ignore known harmless errors
        if "user has not initiated a conversation" in error_msg.lower() or \
           "chat not found" in error_msg.lower() or \
           "message to delete not found" in error_msg.lower() or \
           "message to edit not found" in error_msg.lower() or \
           "user not found" in error_msg.lower() or \
           "user is deactivated" in error_msg.lower() or \
           "bot was blocked" in error_msg.lower() or \
           "user hasn't started" in error_msg.lower() or \
           "forbidden: bot was blocked by the user" in error_msg.lower():
            return

        # Handle timeout errors more gracefully
        if isinstance(error, (TimedOut, NetworkError)):
            logger.warning(f"Network error occurred: {error_msg}")

            # Try to notify user about network issues for non-callback messages
            if update and update.effective_chat and update.message:
                try:
                    await update.message.reply_text(
                        "⚠️ Network issues detected. Please try again later.",
                        read_timeout=10,
                        write_timeout=10,
                        connect_timeout=10
                    )
                except Exception:
                    # Silently ignore if we can't notify the user
                    pass
            return

        # For other errors, log with full details
        logger.error(f"Update {update} caused error {error}", exc_info=context.error)

        # Try to notify user about other errors in private chats
        if update and update.effective_chat and update.effective_chat.type == "private":
            try:
                await update.effective_chat.send_message(
                    "An error occurred while processing your request. Please try again.",
                    read_timeout=10,
                    write_timeout=10,
                    connect_timeout=10
                )
            except Exception:
                # Silently ignore if we can't notify the user
                pass

    except Exception as e:
        logger.error(f"Error in error handler: {e}", exc_info=True)

if __name__ == "__main__":
    main()
