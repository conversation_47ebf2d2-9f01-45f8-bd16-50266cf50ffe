import os
import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ChatMemberUpdated
from telegram.constants import ParseMode
from telegram.error import BadRequest, Forbidden
from telegram.ext import ContextTypes
from config import (
    MAIN_CHANNEL, MAIN_CHANNEL_ID, SECOND_CHANNEL, SECOND_CHANNEL_ID,
    SUPPORT_BOT, BOT_USERNAME, ADMIN_CHAT_ID, ADD_CHANNEL_URL, ADD_GROUP_URL
)
from database import add_user
from database import channels_collection
from database import store_channel
from database import get_channel
from database import has_reached_channel_limit
from database import is_premium_user
from database import remove_channel
from database import verify_channels_for_user
from database import set_premium_status
from database import get_user_channels
from database import can_user_scan
from database import record_scan
from database import get_user_scan_info
from utils import (
    check_channel_membership, safe_send_photo, safe_send_text,
    safe_edit_message, get_photo_path
)
from datetime import datetime, timedelta
from telegram.ext import Application, CommandHandler, CallbackQuery<PERSON><PERSON><PERSON>, ChatJoinRequest<PERSON>and<PERSON>, ChatMemberHandler

# Set up logger with reduced verbosity
logger = logging.getLogger(__name__)
logger.setLevel(logging.WARNING)  # Only log warnings and errors

WELCOME_CAPTION = (
    "┏━━━━━ 🔰 𝗖𝗢𝗣𝗬𝗥𝗜𝗚𝗛𝗧 𝗕𝗢𝗧 🔰 ━━━━━┓\n\n"
    "👋 <b>Welcome to the ultimate protection service!</b>\n\n"
    "📋 <i>I help safeguard your channels & groups against copyright strikes and fake reports.</i>\n\n"
    "┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n"
    "┃     📱 <b>Getting Started</b> 📱     ┃\n"
    "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n\n"
    "1️⃣ <i>Tap the button below to add me to your channel</i>\n"
    "2️⃣ <i>Select your channel or group from the list</i>\n"
    "3️⃣ <i>Grant all required permissions</i>\n"
    "4️⃣ <i>Enjoy full protection services!</i>"
)

JOIN_CHANNELS_CAPTION = (
    "┏━━━━━ ⚠️ 𝗔𝗖𝗧𝗜𝗢𝗡 𝗥𝗘𝗤𝗨𝗜𝗥𝗘𝗗 ⚠️ ━━━━━┓\n\n"
    "Please join both update channels to use this bot.\n\n"
    "👉 <i>Once joined, click the 'Check Joined' button below</i>"
)

# Add a new function to handle my_chat_member updates
async def handle_chat_member_update(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Handle updates to the bot's member status in a chat.
    This function is called when the bot is added to a channel or when its permissions change.
    """
    my_chat_member = update.my_chat_member
    if not my_chat_member:
        return

    # Only process if the bot was added to a channel
    if my_chat_member.chat.type not in ['channel', 'group']:
        return

    # Check if the bot is an admin
    if not my_chat_member.new_chat_member.status == 'administrator':
        return

    # Get bot permissions
    chat_member = my_chat_member.new_chat_member
    has_all_permissions = (
        chat_member.can_post_messages and
        chat_member.can_edit_messages and
        chat_member.can_delete_messages and
        chat_member.can_invite_users and
        chat_member.can_promote_members
    )

    # Get the user who added the bot
    user = my_chat_member.from_user
    if not user:
        return

    # Get channel info
    channel_id = my_chat_member.chat.id
    channel_name = my_chat_member.chat.title or "Unknown Channel"

    # Store user in database (premium status will be false by default)
    add_user(user.id, user.username)

    # Check if user is premium - premium users have unlimited channels
    is_premium = is_premium_user(user.id)

    # Check if user has reached channel limit (only for non-premium users)
    over_limit = False
    show_in_list = True

    if not is_premium and has_reached_channel_limit(user.id):
        # This user has reached their limit, channel will be stored with show_in_list=False
        show_in_list = False
        over_limit = True

        # Send limit reached message to user
        limit_message = (
            "┏━━━━━ ⚠️ LIMIT REACHED ⚠️ ━━━━━┓\n\n"
            f"You've reached the maximum of <b>6 channels</b> for free users.\n\n"
            "┃ <b>Current Status:</b>\n"
            "┃ ⛔ Channel limit reached\n"
            "┃ 🔢 Channels: 6/6 visible\n"
            "┏━━━━━ 💎 𝗨𝗣𝗚𝗥𝗔𝗗𝗘 𝗧𝗢𝗗𝗔𝗬 💎 ━━━━━┓\n"
            "┃ • Unlimited visible channels\n"
            "┃ • Enhanced protection\n"
            "┃ • Priority support\n"
            "┃ • 24/7 Premium Support\n"
            "┃ • Advanced Analytics\n"
            "┃ • VIP Account Status\n"
            "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛"
        )

        # Create button for premium upgrade
        keyboard = [
            [InlineKeyboardButton("💎 Upgrade to Premium", callback_data="premium")]
        ]
        markup = InlineKeyboardMarkup(keyboard)

        try:
            await context.bot.send_message(
                chat_id=user.id,
                text=limit_message,
                parse_mode=ParseMode.HTML,
                reply_markup=markup
            )
        except Forbidden:
            # Silent ignore: Don't log "User hasn't started a conversation with the bot" errors
            pass
        except Exception as e:
            # Only log unexpected errors
            if not isinstance(e, Forbidden):
                logger.error(f"Failed to send limit message to user {user.id}: {e}")

        # Notify admin about limit reached
        admin_message = (
            "┏━━━━━ ⚠️ 𝗟𝗜𝗠𝗜𝗧 𝗥𝗘𝗔𝗖𝗛𝗘𝗗 ⚠️ ━━━━━┓\n\n"
            f"┃ <b>User:</b> {user.id} ({user.username or 'No username'})\n"
            f"┃ <b>Added channel:</b> {channel_name}\n"
            f"┃ <b>Status:</b> Free user, 6-channel limit\n"
            f"┃ <b>Action:</b> Stored with show_in_list=False\n\n"
            f"┃ <i>This is a potential premium upgrade opportunity!</i>\n"
            "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛"
        )
        try:
            await context.bot.send_message(
                chat_id=ADMIN_CHAT_ID,
                text=admin_message,
                parse_mode=ParseMode.HTML
            )
        except Exception as e:
            logger.error(f"Failed to send admin notification: {e}")

    # Store channel information with appropriate visibility setting
    # For premium users, always set show_in_list=True
    is_new = store_channel(
        channel_id=channel_id,
        channel_name=channel_name,
        admin_id=user.id,
        is_admin=True,
        has_all_permissions=has_all_permissions,
        show_in_list=True if is_premium else show_in_list
    )

    # Only send success message if the channel is not over the limit
    if is_new and not over_limit:
        # Send congratulations message to user
        # Add protection status to the heading based on permissions
        protection_status = "✅ FULLY PROTECTED" if has_all_permissions else "⚠️ NOT FULLY PROTECTED"

        congrats_message = (
            f"┏━━━━━ ✅ CHANNEL SECURED! ✅ ━━━━━┓\n\n"
            f"🎉 <b>{channel_name}</b> is now protected!\n"
            f"┃ Status: <b>{protection_status}</b>\n\n"
        )

        if not is_premium:
            congrats_message += (
                "┃ <b>🔰 Basic Protection Active:</b>\n"
                "┃ ┣━ Content monitoring\n"
                "┃ ┣━ Copyright strike prevention\n"
                "┃ ┣━ Fake report blocking\n"
                "┃ ┗━ Basic protection status\n\n"
                "┃ <b>⭐ Want 3x stronger protection?</b>\n"
                "┃ <b>Upgrade to Premium now!</b>\n"
                "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n\n"
            )
        else:
            congrats_message += (
                "┃ <b>💎 Premium Protection Active:</b>\n"
                "┃ ┣━ Advanced content filtering\n"
                "┃ ┣━ 16-layer copyright protection\n"
                "┃ ┣━ VIP processing priority\n"
                "┃ ┗━ Max security coverage\n\n"
                "┃ <b>⭐ Premium benefits activated!</b>\n"
                "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n\n"
            )

        congrats_message += "Use /mychannel to view all your protected channels."

        try:
            await context.bot.send_message(
                chat_id=user.id,
                text=congrats_message,
                parse_mode=ParseMode.HTML
            )
        except Forbidden:
            # Silent ignore: Don't log "User hasn't started a conversation with the bot" errors
            pass
        except Exception as e:
            # Only log unexpected errors
            if not isinstance(e, Forbidden):
                logger.error(f"Failed to send congratulations message to user {user.id}: {e}")

        # Notify admin about new channel
        admin_message = (
            "┏━━━━━ 🆕 𝗡𝗘𝗪 𝗖𝗛𝗔𝗡𝗡𝗘𝗟 ┓\n\n"
            f"┃ <b>Channel:</b> {channel_name}\n"
            f"┃ <b>Channel ID:</b> {channel_id}\n"
            f"┃ <b>Added by:</b> {user.id} ({user.username or 'No username'})\n"
            f"┃ <b>User Status:</b> {'💎 Premium' if is_premium else '🔰 Free'}\n"
            "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛"
        )
        try:
            await context.bot.send_message(
                chat_id=ADMIN_CHAT_ID,
                text=admin_message,
                parse_mode=ParseMode.HTML
            )
        except Exception as e:
            logger.error(f"Failed to send admin notification: {e}")
    elif is_new and over_limit:
        # This is a new channel but over limit - send limited message
        hidden_message = (
            f"┏━━━━━ ℹ️ HIDDEN CHANNEL ℹ️ ━━━━━┓\n\n"
            f"<b>{channel_name}</b> has been added to our system but is hidden due to your free plan limit.\n\n"
            "┃ <b>Current Status:</b>\n"
            "┃ 🔒 Channel: Hidden (over limit)\n"
            "┃ 🔢 Only your first 6 channels are visible\n\n"
            "┃ <b>💎 Upgrade to Premium to:</b>\n"
            "┃ ┣━ Unlock ALL your channels\n"
            "┃ ┣━ Get enhanced protection\n"
            "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛"
        )

        keyboard = [
            [InlineKeyboardButton("💎 Upgrade to Premium", callback_data="premium")],
            [InlineKeyboardButton("📊 My Channels", callback_data="mychannel")]
        ]
        markup = InlineKeyboardMarkup(keyboard)

        try:
            await context.bot.send_message(
                chat_id=user.id,
                text=hidden_message,
                parse_mode=ParseMode.HTML,
                reply_markup=markup
            )
        except Forbidden:
            # Silent ignore: Don't log "User hasn't started a conversation with the bot" errors
            pass
        except Exception as e:
            # Only log unexpected errors
            if not isinstance(e, Forbidden):
                logger.error(f"Failed to send hidden channel message to user {user.id}: {e}")
    elif not is_new:
        # Channel was already added before, just send an update message
        # Check if the channel has all required permissions and construct appropriate message
        if has_all_permissions:
            protection_status = "✅ FULLY PROTECTED"
        else:
            protection_status = "⚠️ NOT FULLY PROTECTED"

        update_message = (
            "┏━━━━━ 🔄 𝗖𝗛𝗔𝗡𝗡𝗘𝗟 𝗨𝗣𝗗𝗔𝗧𝗘𝗗 🔄 ━━━━━┓\n\n"
            f"┃ <b>{channel_name}</b> has been updated.\n"
            f"┃ Protection status: <b>{protection_status}</b>\n"
            "┃ Settings refreshed successfully.\n\n"
            "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n\n"
            "Use /mychannel to view all your protected channels."
        )
        try:
            await context.bot.send_message(
                chat_id=user.id,
                text=update_message,
                parse_mode=ParseMode.HTML
            )
        except Forbidden:
            # Silent ignore: Don't log "User hasn't started a conversation with the bot" errors
            pass
        except Exception as e:
            # Only log unexpected errors
            if not isinstance(e, Forbidden):
                logger.error(f"Failed to send update message to user {user.id}: {e}")

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    /start command:
    - Check if the user has joined both the main and secondary update channels.
    - If not joined, send channel.jpg with a caption asking to join both channels plus inline buttons.
    - If joined, send wel.jpg with the welcome caption.
    - Only stores user data if the welcome message is successfully sent.
    """
    user = update.effective_user

    # Check membership in both channels
    joined_main = await check_channel_membership(context.bot, user.id, MAIN_CHANNEL_ID)
    joined_second = await check_channel_membership(context.bot, user.id, SECOND_CHANNEL_ID)

    try:
        if joined_main and joined_second:
            # User has joined both channels, send welcome message with wel.jpg
            keyboard = [
                [InlineKeyboardButton("📢 Protect Your Channel", url=ADD_CHANNEL_URL)],
                [InlineKeyboardButton("👥 Protect Your Group", url=ADD_GROUP_URL)],
                [InlineKeyboardButton("❓ Help", callback_data="help_command")]
            ]
            markup = InlineKeyboardMarkup(keyboard)
            await safe_send_photo(update, context, "wel.jpg", WELCOME_CAPTION, markup)

            # Add the user to the database only after successfully sending the welcome message
            add_user(user.id, user.username)
            logger.info(f"User {user.id} added to database after successful welcome message")
        else:
            # User has not joined both channels, send join request message
            keyboard = [
                [InlineKeyboardButton("Join Main Channel", url=MAIN_CHANNEL)],
                [InlineKeyboardButton("Join Second Channel", url=SECOND_CHANNEL)],
                [InlineKeyboardButton("Check Joined", callback_data="check_joined")]
            ]
            markup = InlineKeyboardMarkup(keyboard)
            await safe_send_photo(update, context, "channel.jpg", JOIN_CHANNELS_CAPTION, markup)

            # Add the user to the database only after successfully sending the join request message
            add_user(user.id, user.username)
            logger.info(f"User {user.id} added to database after successful join request message")
    except Exception as e:
        # If there was an error sending the message, don't store the user data
        logger.error(f"Failed to send message to user {user.id}, not storing in database: {e}")
        # Re-raise the exception to be handled by the global error handler
        raise

async def check_joined_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    'Check Joined' callback:
    - Recheck the user's membership for both channels.
    - If joined, delete the join message and send the welcome message.
    - If not, alert the user that they haven't joined both channels.
    - Only stores user data if the welcome message is successfully sent.
    """
    query = update.callback_query
    user = query.from_user
    await query.answer()

    # Recheck membership in both channels
    joined_main = await check_channel_membership(context.bot, user.id, MAIN_CHANNEL_ID)
    joined_second = await check_channel_membership(context.bot, user.id, SECOND_CHANNEL_ID)

    try:
        if joined_main and joined_second:
            # Both channels joined; delete join message and send welcome message
            try:
                await query.message.delete()
            except Exception as e:
                logger.warning(f"Failed to delete join message: {e}")

            keyboard = [
                [InlineKeyboardButton("📢 Protect Your Channel", url=ADD_CHANNEL_URL)],
                [InlineKeyboardButton("👥 Protect Your Group", url=ADD_GROUP_URL)],
                [InlineKeyboardButton("❓ Help", callback_data="help_command")]
            ]
            markup = InlineKeyboardMarkup(keyboard)
            await safe_send_photo(update, context, "wel.jpg", WELCOME_CAPTION, markup)

            # Add the user to the database only after successfully sending the welcome message
            add_user(user.id, user.username)
            logger.info(f"User {user.id} added to database after successful welcome message in check_joined_callback")
        else:
            # Not joined: re-send join message with an alert
            text = "🚫 You haven't joined both update channels yet. Please join both channels and then click 'Check Joined'."
            keyboard = [
                [InlineKeyboardButton("Join Main Channel", url=MAIN_CHANNEL)],
                [InlineKeyboardButton("Join Second Channel", url=SECOND_CHANNEL)],
                [InlineKeyboardButton("Check Joined", callback_data="check_joined")]
            ]
            markup = InlineKeyboardMarkup(keyboard)
            await safe_edit_message(update, context, text, markup)
    except Exception as e:
        # If there was an error sending the message, don't store the user data
        logger.error(f"Failed to send message to user {user.id} in check_joined_callback, not storing in database: {e}")
        # Re-raise the exception to be handled by the global error handler
        raise

async def start_process_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Callback for starting process (retained for other functionalities).
    """
    query = update.callback_query
    await query.answer()

    text = "🚫 Please join our updates channels to use the bot."
    keyboard = [
        [InlineKeyboardButton("🔔 Main Update Channel", url=MAIN_CHANNEL)],
        [InlineKeyboardButton("🔔 Secondary Update Channel", url=SECOND_CHANNEL)],
        [InlineKeyboardButton("✅ I Have Joined", callback_data="i_have_joined")]
    ]
    markup = InlineKeyboardMarkup(keyboard)
    await safe_edit_message(update, context, text, markup)

async def i_have_joined_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Callback for when a user indicates they have joined both channels.
    Only stores user data if the welcome message is successfully sent.
    """
    query = update.callback_query
    await query.answer()
    user = query.from_user
    logger.info(f"i_have_joined_callback triggered for user {user.id}")

    # Check membership in both channels
    joined_main = await check_channel_membership(context.bot, user.id, MAIN_CHANNEL_ID)
    joined_second = await check_channel_membership(context.bot, user.id, SECOND_CHANNEL_ID)

    try:
        if joined_main and joined_second:
            logger.info(f"User {user.id} confirmed as joined in both channels")
            text = (
                "Thank you for joining both updates channel.\n\n"
                "You can now use the bot, please click /start or /mychannel."
            )
            await safe_send_text(update, context, text)

            # Add the user to the database only after successfully sending the welcome message
            add_user(user.id, user.username)
            logger.info(f"User {user.id} added to database after successful welcome message in i_have_joined_callback")
        else:
            text = "🚫 You haven't joined both update channels yet. Please join both and then tap <b>I Have Joined</b>."
            keyboard = [
                [InlineKeyboardButton("🔔 Main Update Channel", url=MAIN_CHANNEL)],
                [InlineKeyboardButton("🔔 Secondary Update Channel", url=SECOND_CHANNEL)],
                [InlineKeyboardButton("✅ I Have Joined", callback_data="i_have_joined")]
            ]
            markup = InlineKeyboardMarkup(keyboard)
            await safe_edit_message(update, context, text, markup)
    except Exception as e:
        # If there was an error sending the message, don't store the user data
        logger.error(f"Failed to send message to user {user.id} in i_have_joined_callback, not storing in database: {e}")
        # Re-raise the exception to be handled by the global error handler
        raise

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Send a helpful message explaining how to use the bot."""
    help_text = (
        "┏━━━━━ 📚 𝗛𝗘𝗟𝗣 𝗠𝗘𝗡𝗨 📚 ━━━━━┓\n\n"
        "👋 <b>Welcome to the Copyright Bot Help Menu</b>\n\n"
        "This bot helps protect your channels and groups against copyright violations and reports.\n\n"
        "<b>🔰 Main Commands:</b>\n"
        "• /start - Start the bot and view main menu\n"
        "• /help - Show this help message\n"
        "• /mychannel - View and manage your channels\n"
        "• /premium - View premium features and upgrade\n"
        "• /stats - View global protection statistics\n\n"

        "<b>💬 For more help, contact:</b> @" + SUPPORT_BOT
    )

    # Check if this is from a callback query (button click)
    if update.callback_query:
        # Answer the callback query to stop the loading animation
        await update.callback_query.answer()

        # Instead of editing the message (which would fail for media messages),
        # send a new message with the help text
        await update.callback_query.message.reply_text(
            help_text,
            parse_mode=ParseMode.HTML
        )
    else:
        # Regular command handler - just send the help text
        await update.message.reply_text(
            help_text,
            parse_mode=ParseMode.HTML
        )

async def mychannel_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Show list of channels that belong to the user with management options"""
    try:
        user_id = update.effective_user.id

        # Check if user is premium - store result to use multiple times
        is_premium = is_premium_user(user_id)

        # Get user's channels - for premium users get all channels, for free users only visible ones
        user_channels = get_user_channels(user_id, only_visible=True)

        if not user_channels:
            # Send message with no channels
            keyboard = [
                [InlineKeyboardButton("➕ ADD CHANNEL", url=ADD_CHANNEL_URL)],
                [InlineKeyboardButton("➕ ADD GROUP", url=ADD_GROUP_URL)],
                [InlineKeyboardButton("💬 SUPPORT", url=f"https://t.me/{SUPPORT_BOT}")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            text = (
                "┏━━━━━ 😔 NO CHANNELS YET 😔 ━━━━━┓\n\n"
                "┃ Add your first channel and get\n"
                "┃ copyright protection!\n"
                "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛"
            )

            await update.message.reply_text(
                text,
                reply_markup=reply_markup,
                parse_mode=ParseMode.HTML
            )
            return

        # Format the channel list with emojis and better formatting
        count = len(user_channels)
        channels_text = "┏━━━━━ 📊 YOUR CHANNELS 📊 ━━━━━┓\n\n"

        # Add premium status info if applicable
        if is_premium:
            channels_text += (
                "┃ 💎 <b>Premium Status:</b> Active ✓\n"
                f"┃ 🔢 <b>Channels:</b> {count} / ∞\n"
                "┃ 🛡️ <b>Protection:</b> Advanced\n"
                "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n\n"
            )
        else:
            # Get total channel count including hidden ones
            total_channels = channels_collection.count_documents({"admin_id": user_id})
            hidden_channels = total_channels - count if total_channels > count else 0

            channels_text += (
                "┃ 🔰 <b>Free Status:</b> Active ✓\n"
                f"┃ 🔢 <b>Channels:</b> {count}/6\n"
                f"┃ 🔒 <b>Not Protected Channels:</b> {hidden_channels}\n"
                "┃ 🛡️ <b>Protection:</b> Basic\n"
                "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n\n"
            )

        # Generate list of channels with numbers
        channel_list = []
        has_unprotected_channels = False
        for i, ch in enumerate(user_channels, 1):
            if not ch.get('is_admin', False):
                protection_status = "⚠️ Not Protected"
                has_unprotected_channels = True
            elif not ch.get('has_all_permissions', False):
                protection_status = "⚠️ Not Fully Protected"
                has_unprotected_channels = True
            else:
                protection_status = "✅ Fully Protected"

            channel_list.append(
                f"<b>#{i}</b> ┃ <b>{ch.get('channel_name', 'Unknown')}</b>\n"
                f"    ┣━ <b>Status:</b> {protection_status}\n"
                f"    ┗━ <b>ID:</b> <code>{ch.get('channel_id', 'N/A')}</code>"
            )

        text = channels_text + "\n\n".join(channel_list)

        # Add a warning about unprotected channels
        if has_unprotected_channels:
            text += (
                "\n\n⚠️ <b>WARNING: Some channels are not fully protected!</b>\n"
                "To enable full protection:\n"
                "1. Open your channel settings\n"
                "2. Go to Administrators section\n"
                "3. Edit this bot's permissions\n"
                "4. Enable ALL permissions (especially Post, Edit, Delete, Invite, Add Admins)\n"
                "5. Come back and tap 'Refresh List'"
            )

        # Add a prompt to add more channels if not at limit
        if not is_premium and count < 6:
            text += f"\n\n📢 <i>You can add {6-count} more channels with your free plan!</i>"
        elif not is_premium and hidden_channels > 0:
            text += f"\n\n⭐ <i>Upgrade to Premium to unlock {hidden_channels} more channels!</i>"

        # Main buttons
        keyboard = [
            [InlineKeyboardButton("🔄 Refresh List", callback_data="refresh_channels")],
            [InlineKeyboardButton("➕ Add Channel", url=ADD_CHANNEL_URL)],
            [InlineKeyboardButton("➕ Add Group", url=ADD_GROUP_URL)],
        ]

        # Add premium button for non-premium users
        if not is_premium:
            keyboard.append([InlineKeyboardButton("💎 Upgrade to Premium", callback_data="premium")])

        markup = InlineKeyboardMarkup(keyboard)
        await update.message.reply_text(
            text,
            reply_markup=markup,
            parse_mode=ParseMode.HTML
        )
    except Exception as e:
        logging.error(f"Error in mychannel_command: {e}")
        await update.message.reply_text(
            "❌ <b>Error:</b> There was a problem retrieving your channels.\n\n<i>Please try again later.</i>",
            parse_mode=ParseMode.HTML
        )

async def mychannel_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Callback handler for the mychannel button.
    Shows a list of the user's channels with protection status.
    """
    query = update.callback_query
    await query.answer()
    user = query.from_user
    user_id = user.id

    try:
        # Check if user is premium
        is_premium = is_premium_user(user_id)

        # Get user's channels - for premium users, get all channels
        user_channels = get_user_channels(user_id, only_visible=True)

        if user_channels:
            # Format the channel list with emojis and better formatting
            count = len(user_channels)
            channels_text = "┏━━━━━ 📊 YOUR CHANNELS 📊 ━━━━━┓\n\n"

            # Add premium status info if applicable
            if is_premium:
                channels_text += (
                    "┃ 💎 <b>Premium Status:</b> Active ✓\n"
                    f"┃ 🔢 <b>Channels:</b> {count} / ∞\n"
                    "┃ 🛡️ <b>Protection:</b> Advanced\n"
                    "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n\n"
                )
            else:
                # Get total channel count including hidden ones
                total_channels = channels_collection.count_documents({"admin_id": user_id})
                hidden_channels = total_channels - count if total_channels > count else 0

                channels_text += (
                    "┃ 🔰 <b>Free Status:</b> Active ✓\n"
                    f"┃ 🔢 <b>Channels:</b> {count}/6\n"
                    f"┃ 🔒 <b>Hidden Channels:</b> {hidden_channels}\n"
                    "┃ 🛡️ <b>Protection:</b> Basic\n"
                    "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n\n"
                )

            # Generate list of channels with numbers
            channel_list = []
            has_unprotected_channels = False
            for i, ch in enumerate(user_channels, 1):
                if not ch.get('is_admin', False):
                    protection_status = "⚠️ Not Protected"
                    has_unprotected_channels = True
                elif not ch.get('has_all_permissions', False):
                    protection_status = "⚠️ Not Fully Protected"
                    has_unprotected_channels = True
                else:
                    protection_status = "✅ Fully Protected"

                channel_list.append(
                    f"<b>#{i}</b> ┃ <b>{ch.get('channel_name', 'Unknown')}</b>\n"
                    f"    ┣━ <b>Status:</b> {protection_status}\n"
                    f"    ┗━ <b>ID:</b> <code>{ch.get('channel_id', 'N/A')}</code>"
                )

            text = channels_text + "\n\n".join(channel_list)

            # Add a warning about unprotected channels
            if has_unprotected_channels:
                text += (
                    "\n\n⚠️ <b>WARNING: Some channels are not fully protected!</b>\n"
                    "To enable full protection:\n"
                    "1. Open your channel settings\n"
                    "2. Go to Administrators section\n"
                    "3. Edit this bot's permissions\n"
                    "4. Enable ALL permissions (especially Post, Edit, Delete, Invite, Add Admins)\n"
                    "5. Come back and tap 'Refresh List'"
                )

            # Add tip about refreshing
            text += "\n\n💡 <i>Tip: Use the Refresh button to verify channel status</i>"

            # Add a prompt to add more channels if not at limit
            if not is_premium and count < 6:
                text += f"\n\n📢 <i>You can add {6-count} more channels with your free plan!</i>"
            elif not is_premium and hidden_channels > 0:
                text += f"\n\n⭐ <i>Upgrade to Premium to unlock {hidden_channels} more channels!</i>"

            # Main buttons
            keyboard = [
                [InlineKeyboardButton("🔄 Refresh List", callback_data="refresh_channels")],
                [InlineKeyboardButton("➕ Add Channel", url=ADD_CHANNEL_URL)],
                [InlineKeyboardButton("➕ Add Group", url=ADD_GROUP_URL)],
            ]

            # Add premium button for non-premium users
            if not is_premium:
                keyboard.append([InlineKeyboardButton("💎 Upgrade to Premium", callback_data="premium")])
        else:
            text = (
                "┏━━━━━ 😔 NO CHANNELS YET 😔 ━━━━━┓\n\n"
                "┃ Add your first channel and get\n"
                "┃ copyright protection!\n"
                "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛"
            )

            keyboard = [
                [InlineKeyboardButton("➕ Add Channel", url=ADD_CHANNEL_URL)],
                [InlineKeyboardButton("➕ Add Group", url=ADD_GROUP_URL)]
            ]

        markup = InlineKeyboardMarkup(keyboard)

        try:
            await query.edit_message_text(text=text, reply_markup=markup, parse_mode=ParseMode.HTML)
        except BadRequest as e:
            # If message content is identical, just return
            if "Message is not modified" in str(e):
                return
            else:
                # For other errors, send a new message
                await query.message.reply_text(text=text, reply_markup=markup, parse_mode=ParseMode.HTML)
    except Exception as e:
        logger.error(f"Error in mychannel_callback: {e}")
        await query.message.reply_text(
            "❌ There was an error retrieving your channels. Please try again later.",
            parse_mode=ParseMode.HTML
        )

async def premium_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Provides information about premium features.
    Shows different messages for premium vs non-premium users.
    """
    user_id = update.effective_user.id

    # Check if user is premium
    is_premium = is_premium_user(user_id)

    if is_premium:
        # Message for premium users
        text = (
            "┏━━━━━ 💎 𝗣𝗥𝗘𝗠𝗜𝗨𝗠 𝗦𝗧𝗔𝗧𝗨𝗦 💎 ━━━━━┓\n\n"
            "✨ <b>Congratulations!</b> Your premium status is <b>ACTIVE</b> ✅\n\n"
            "┏━━━━━━━ 🌟 𝗬𝗢𝗨𝗥 𝗕𝗘𝗡𝗘𝗙𝗜𝗧𝗦 🌟 ━━━━━━━┓\n"
            "┃ ✓ <b>16-Layer Protection</b> - ACTIVE\n"
            "┃ ✓ <b>Unlimited Channels</b> - ACTIVE\n"
            "┃ ✓ <b>Priority Processing</b> - ACTIVE\n"
            "┃ ✓ <b>24/7 Premium Support</b> - ACTIVE\n"
            "┃ ✓ <b>Advanced Analytics</b> - ACTIVE\n"
            "┃ ✓ <b>VIP Account Status</b> - ACTIVE\n"
            "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n\n"
            "👑 <b>Thank you for being a valued Premium member!</b>\n"
            "Your channels have the highest level of protection available."
        )

        keyboard = [
            [InlineKeyboardButton("📊 My Channels", callback_data="mychannel")]
        ]
    else:
        # Message for non-premium users
        text = (
            "┏━━━━━ 💎 𝗣𝗥𝗘𝗠𝗜𝗨𝗠 𝗣𝗥𝗢𝗧𝗘𝗖𝗧𝗜𝗢𝗡 💎 ━━━━━┓\n\n"
            "🚀 <b>Elevate your channel's security to the next level!</b>\n\n"
            "┏━━━━━━━ ⚡ 𝗪𝗛𝗬 𝗨𝗣𝗚𝗥𝗔𝗗𝗘 ⚡ ━━━━━━━━┓\n"
            "┃ ✓ <b>16-Layer Protection</b> (vs 5 layers)\n"
            "┃ ✓ <b>Unlimited Channels</b> (vs 6 max)\n"
            "┃ ✓ <b>Priority Processing</b> (3x faster)\n"
            "┃ ✓ <b>24/7 Premium Support</b>\n"
            "┃ ✓ <b>Advanced Analytics</b>\n"
            "┃ ✓ <b>VIP Account Status</b>\n"
            "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n\n"
            "💯 <b>Don't risk your channels getting banned!</b>\n"
            "⚡ <b>Our premium users report 99% fewer issues</b> ⚡"
        )

        keyboard = [
            [InlineKeyboardButton("🔒 Secure My Channels Now", callback_data="choose_payment")]
        ]

    markup = InlineKeyboardMarkup(keyboard)

    if update.message:
        await update.message.reply_text(text, reply_markup=markup, parse_mode=ParseMode.HTML)
    elif update.callback_query:
        await update.callback_query.answer()
        try:
            await update.callback_query.edit_message_text(text, reply_markup=markup, parse_mode=ParseMode.HTML)
        except BadRequest as e:
            if "message to edit" in str(e):
                await update.callback_query.message.reply_text(text, reply_markup=markup, parse_mode=ParseMode.HTML)

async def premium_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Callback to show premium information.
    """
    await update.callback_query.answer()
    await premium_command(update, context)

async def refresh_mychannel_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Refresh the mychannel list when the refresh button is clicked"""
    query = update.callback_query

    try:
        user_id = update.effective_user.id

        # Check if user is premium
        is_premium = is_premium_user(user_id)

        # First, get all user's channels from database
        user_channels = get_user_channels(user_id, only_visible=True)

        # Verify each channel's existence and permission status
        valid_channel_ids = []
        channels_updated = False
        permissions_changed = False

        # Show a temporary "refreshing" message
        await query.answer(text="🔄 Refreshing channels list...", show_alert=False)

        # Check each channel
        for channel in user_channels:
            channel_id = channel.get('channel_id')
            channel_name = channel.get('channel_name', 'Unknown')
            try:
                # Check if bot is still in the channel
                chat_member = await context.bot.get_chat_member(channel_id, context.bot.id)

                # Check if bot is still an admin
                is_admin = chat_member.status == 'administrator'

                # Log fetched permissions
                if is_admin:
                    logger.warning(
                        f"REFRESH: Channel {channel_name} ({channel_id}) - Fetched Admin Permissions:\n"
                        f"  can_post_messages: {getattr(chat_member, 'can_post_messages', 'N/A')}\n"
                        f"  can_edit_messages: {getattr(chat_member, 'can_edit_messages', 'N/A')}\n"
                        f"  can_delete_messages: {getattr(chat_member, 'can_delete_messages', 'N/A')}\n"
                        f"  can_invite_users: {getattr(chat_member, 'can_invite_users', 'N/A')}\n"
                        f"  can_promote_members: {getattr(chat_member, 'can_promote_members', 'N/A')}"
                    )
                else:
                    logger.warning(f"REFRESH: Channel {channel_name} ({channel_id}) - Bot is NOT admin.")

                # Check if bot has all required permissions
                has_all_permissions = False
                if is_admin:
                    has_all_permissions = (
                        chat_member.can_post_messages and
                        chat_member.can_edit_messages and
                        chat_member.can_delete_messages and
                        chat_member.can_invite_users and
                        chat_member.can_promote_members
                    )
                logger.warning(f"REFRESH: Channel {channel_name} ({channel_id}) - Calculated has_all_permissions: {has_all_permissions}")

                # Check if permissions have changed
                old_is_admin = channel.get('is_admin', False)
                old_has_all_permissions = channel.get('has_all_permissions', False)

                if old_is_admin != is_admin or old_has_all_permissions != has_all_permissions:
                    permissions_changed = True
                    logger.info(f"Channel {channel_name} permissions changed: admin={is_admin}, all_permissions={has_all_permissions}")

                # Update channel status in database if changed
                if old_is_admin != is_admin or old_has_all_permissions != has_all_permissions:
                    channels_collection.update_one(
                        {"channel_id": channel_id},
                        {"$set": {
                            "is_admin": is_admin,
                            "has_all_permissions": has_all_permissions,
                            "updated_at": datetime.utcnow()
                        }}
                    )
                    channels_updated = True
                    logger.warning(f"REFRESH: Channel {channel_name} ({channel_id}) - DB updated with has_all_permissions: {has_all_permissions}")

                    # Notify user about permission changes
                    if old_has_all_permissions != has_all_permissions:
                        try:
                            if has_all_permissions:
                                status_msg = (
                                    f"✅ <b>{channel_name}</b> is now FULLY PROTECTED!\n"
                                    "All required permissions are now granted."
                                )
                            else:
                                status_msg = (
                                    f"⚠️ <b>{channel_name}</b> is NOT FULLY PROTECTED!\n"
                                    "Some required permissions are missing."
                                )

                            await context.bot.send_message(
                                chat_id=user_id,
                                text=status_msg,
                                parse_mode=ParseMode.HTML
                            )
                        except Exception as e:
                            logger.error(f"Failed to send permission change notification: {e}")

                # Add to valid channel IDs list
                valid_channel_ids.append(channel_id)

            except (BadRequest, Forbidden) as e:
                # Bot is no longer in the channel or doesn't have access
                logger.warning(f"Channel access error during refresh: {e}")
                channels_updated = True
                # Don't add to valid_channel_ids - will be removed
            except Exception as e:
                logger.error(f"Error checking channel {channel_id}: {e}")
                # Add to valid list to avoid removing on error
                valid_channel_ids.append(channel_id)

        # Remove channels that are no longer valid
        removed_channels = verify_channels_for_user(user_id, valid_channel_ids)

        # Get updated channel list after verification
        user_channels = get_user_channels(user_id, only_visible=True)

        # Show appropriate notification based on results
        if removed_channels:
            await query.answer(text=f"✅ Refreshed! {len(removed_channels)} channel(s) removed.", show_alert=True)
        elif channels_updated:
            await query.answer(text="✅ Refreshed! Channel permissions updated.", show_alert=True)
        else:
            await query.answer(text="✅ List Refreshed Successfully!", show_alert=True)

        if not user_channels:
            # Update message with no channels
            keyboard = [
                [InlineKeyboardButton("➕ ADD CHANNEL", url=ADD_CHANNEL_URL)],
                [InlineKeyboardButton("➕ ADD GROUP", url=ADD_GROUP_URL)],
                [InlineKeyboardButton("💬 SUPPORT", url=f"https://t.me/{SUPPORT_BOT}")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            text = (
                "┏━━━━━ 😔 NO CHANNELS YET 😔 ━━━━━┓\n\n"
                "┃ Add your first channel and get\n"
                "┃ copyright protection!\n"
                "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛"
            )

            await query.edit_message_text(
                text,
                reply_markup=reply_markup,
                parse_mode=ParseMode.HTML
            )
            return

        # Format the channel list with emojis and better formatting
        count = len(user_channels)
        channels_text = "┏━━━━━ 📊 YOUR CHANNELS 📊 ━━━━━┓\n\n"

        # Add last refreshed timestamp
        refresh_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        channels_text += f"┃ 🕒 <i>Last refreshed: {refresh_time}</i>\n\n"

        # Add premium status info if applicable
        if is_premium:
            channels_text += (
                "┃ 💎 <b>Premium Status:</b> Active ✓\n"
                f"┃ 🔢 <b>Channels:</b> {count} / ∞\n"
                "┃ 🛡️ <b>Protection:</b> Advanced\n"
                "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n\n"
            )
        else:
            # Get total channel count including hidden ones
            total_channels = channels_collection.count_documents({"admin_id": user_id})
            hidden_channels = total_channels - count if total_channels > count else 0

            channels_text += (
                "┃ 🔰 <b>Free Status:</b> Active ✓\n"
                f"┃ 🔢 <b>Channels:</b> {count}/6\n"
                f"┃ 🔒 <b>Hidden Channels:</b> {hidden_channels}\n"
                "┃ 🛡️ <b>Protection:</b> Basic\n"
                "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n\n"
            )

        # Generate list of channels with numbers
        channel_list = []
        has_unprotected_channels = False
        for i, ch in enumerate(user_channels, 1):
            # Log the permissions being read from DB for display
            db_has_all_permissions = ch.get('has_all_permissions', False)
            db_is_admin = ch.get('is_admin', False)
            logger.warning(f"REFRESH DISPLAY: Channel {ch.get('channel_name', 'Unknown')} ({ch.get('channel_id', 'N/A')}) - Reading from DB: is_admin={db_is_admin}, has_all_permissions={db_has_all_permissions}")

            if not db_is_admin:
                protection_status = "⚠️ Not Protected"
                has_unprotected_channels = True
            elif not db_has_all_permissions:
                protection_status = "⚠️ Not Fully Protected"
                has_unprotected_channels = True
            else:
                protection_status = "✅ Fully Protected"

            channel_list.append(
                f"<b>#{i}</b> ┃ <b>{ch.get('channel_name', 'Unknown')}</b>\n"
                f"    ┣━ <b>Status:</b> {protection_status}\n"
                f"    ┗━ <b>ID:</b> <code>{ch.get('channel_id', 'N/A')}</code>"
            )

        text = channels_text + "\n\n".join(channel_list)

        # Add a warning about unprotected channels
        if has_unprotected_channels:
            text += (
                "\n\n⚠️ <b>WARNING: Some channels are not fully protected!</b>\n"
                "To enable full protection:\n"
                "1. Open your channel settings\n"
                "2. Go to Administrators section\n"
                "3. Edit this bot's permissions\n"
                "4. Enable ALL permissions (especially Post, Edit, Delete, Invite, Add Admins)\n"
                "5. Come back and tap 'Refresh List'"
            )

        # Add a prompt to add more channels if not at limit
        if not is_premium and count < 6:
            text += f"\n\n📢 <i>You can add {6-count} more channels with your free plan!</i>"
        elif not is_premium and hidden_channels > 0:
            text += f"\n\n⭐ <i>Upgrade to Premium to unlock {hidden_channels} more channels!</i>"

        # Add tip about refreshing
        text += "\n\n💡 <i>Tip: Use the Refresh button to verify channel status</i>"

        # Main buttons
        keyboard = [
            [InlineKeyboardButton("🔄 Refresh List", callback_data="refresh_channels")],
            [InlineKeyboardButton("➕ Add Channel", url=ADD_CHANNEL_URL)],
            [InlineKeyboardButton("➕ Add Group", url=ADD_GROUP_URL)],
        ]

        # Add premium button for non-premium users
        if not is_premium:
            keyboard.append([InlineKeyboardButton("💎 Upgrade to Premium", callback_data="premium")])

        markup = InlineKeyboardMarkup(keyboard)
        await query.edit_message_text(
            text,
            reply_markup=markup,
            parse_mode=ParseMode.HTML
        )
    except Exception as e:
        logging.error(f"Error in refresh_mychannel_callback: {e}")
        # Send a more user-friendly error message
        try:
            await query.edit_message_text(
                "❌ <b>Error:</b> There was a problem refreshing your channels.\n\n<i>Please try again later.</i>",
                parse_mode=ParseMode.HTML,
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("🔄 Try Again", callback_data="refresh_channels")],
                    [InlineKeyboardButton("« Back", callback_data="mychannel")]
                ])
            )
        except BadRequest:
            # If message content didn't change
            pass

async def choose_payment_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Callback to show payment options and premium benefits.
    """
    query = update.callback_query
    await query.answer()
    user_id = query.from_user.id
    username = query.from_user.username or "unknown"

    # Get current channel stats for the message
    total_channels = channels_collection.count_documents({"admin_id": user_id})
    visible_channels = len(get_user_channels(user_id, only_visible=True))
    hidden_channels = total_channels - visible_channels

    payment_text = (
        "┏━━━━━ 💎 𝗣𝗥𝗘𝗠𝗜𝗨𝗠 𝗕𝗘𝗡𝗘𝗙𝗜𝗧𝗦 💎 ━━━━━┓\n"
        "┃ ✅ Protect up to 20 channels\n"
        "┃ ✅ Advanced 16-layer protection (99% efficiency)\n"
        "┃ ✅ Automatic backup of all media files\n"
        "┃ ✅ Priority processing (3x faster)\n"
        "┃ ✅ 24/7 dedicated support\n"
        "┃ ✅ Advanced analytics dashboard\n"
        "┃ ✅ Custom channel settings\n"
        "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n\n"

        f"Your Current Status:\n"
        f"┣━ Added Channels: {visible_channels}/6 visible\n"
        f"┣━ Not Protected: {hidden_channels} channels\n"
        "┗━ Protection Level: Basic (5-Layers / 65% efficiency)\n\n"

        "┏━━━━━━━━ 💎 𝗣𝗥𝗘𝗠𝗜𝗨𝗠 𝗣𝗟𝗔𝗡 💎 ━━━━━━━━┓\n"
        "┃ ✨ 『 𝐄𝐥𝐢𝐭𝐞 𝐏𝐫𝐞𝐦𝐢𝐮𝐦 𝐀𝐜𝐜𝐞𝐬𝐬 』 ✨\n"
        "┃\n"
        "┃ 🌕 Annual Premium Access:\n"
        "┃     $99 USD\n"
        "┃     » \"Complete protection for all your channels\"\n"
        "┃     » \"Unlimited channels & premium support\"\n"
        "┃     » \"Advanced security features\"\n"
        "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n\n"

        "⚡ 𝐋𝐈𝐌𝐈𝐓𝐄𝐃 𝐓𝐈𝐌𝐄 𝐎𝐅𝐅𝐄𝐑: 𝐀𝐂𝐓 𝐍𝐎𝐖! ⚡"
    )

    # Define buttons for payment options and navigation
    keyboard = []
    keyboard.append([InlineKeyboardButton("💰 PAY WITH CRYPTO", callback_data="crypto_payment")])
    keyboard.append([InlineKeyboardButton("💬 Contact Support", url=f"https://t.me/{SUPPORT_BOT}")])
    keyboard.append([InlineKeyboardButton("« Back to Premium Info", callback_data="premium")])

    # Send admin notification about potential customer
    try:
        admin_text = (
            "💰 <b>PAYMENT REQUEST</b> 💰\n\n"
            f"User: {user_id} (@{username})\n"
            f"Channels: {total_channels} total, {visible_channels} visible\n"
            f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            "This user clicked on payment options. Follow up!"
        )
        await context.bot.send_message(
            chat_id=ADMIN_CHAT_ID,
            text=admin_text,
            parse_mode=ParseMode.HTML
        )
    except Exception as e:
        logger.error(f"Failed to send admin notification: {e}")

    await query.edit_message_text(
        text=payment_text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode=ParseMode.HTML
    )

async def show_payment_methods_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Show available payment methods and instructions
    """
    query = update.callback_query
    await query.answer()

    # Redirect directly to crypto payment
    await crypto_payment_callback(update, context)

# UPI payment callback removed - only using crypto payments

async def crypto_payment_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Show cryptocurrency payment details
    """
    query = update.callback_query
    await query.answer()

    crypto_text = (
        "┏━━━━━ 💎 CRYPTO PAYMENT 💎 ━━━━━┓\n\n"
        "🔹 USDT Payment Details\n"
        "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
        "💰 Amount: $99 USD\n"
        "🌐 Network: BNB Smart Chain (BEP20)\n"
        "📝 Deposit Address:\n"
        "******************************************\n\n"

        "⚠️ Important Instructions:\n"
        "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
        "• Send exact amount only\n"
        "• Use BEP20 network only\n"
        "• Send payment screenshot\n\n"

        "💡 Premium Benefits:\n"
        "• Unlimited channels\n"
        "• Advanced protection\n"
        "• Priority support\n\n"

        "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛"
    )

    keyboard = [
        [InlineKeyboardButton("💬 Contact Support", url=f"https://t.me/{SUPPORT_BOT}")],
        [InlineKeyboardButton("❓ Need Help?", url=f"https://t.me/{SUPPORT_BOT}")],
        [InlineKeyboardButton("« Back", callback_data="show_payment_methods")]
    ]
    markup = InlineKeyboardMarkup(keyboard)

    try:
        await query.edit_message_text(
            text=crypto_text,
            reply_markup=markup,
            parse_mode=ParseMode.HTML
        )
    except BadRequest as e:
        logger.error(f"Error editing message in crypto_payment_callback: {e}")
        # If there's an error (e.g., message can't be edited), send a new message
        await query.message.reply_text(
            text=crypto_text,
            reply_markup=markup,
            parse_mode=ParseMode.HTML
        )

# PayPal payment callback removed - only using crypto payments

async def join_request_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Handles join requests on channels or groups.
    When a user sends a join request to a channel where the bot is admin,
    the bot sends a welcome message to the user's private chat without approving the request.
    Only stores user data if the welcome message is successfully sent.
    """
    if not update.chat_join_request:
        logger.error("join_request_handler called but update.chat_join_request is None")
        return

    join_request = update.chat_join_request
    user = join_request.from_user
    chat = join_request.chat

    logger.info(f"Received join request from user {user.id} ({user.username}) for chat {chat.id} ({chat.title})")

    # Send welcome message to the user's private chat
    welcome_text = (
        "┏━━━━━ 👋 𝗪𝗘𝗟𝗖𝗢𝗠𝗘 👋 ━━━━━┓\n\n"
        "Hello There! I'm the Copyright Protection Bot – your channel's guardian.\n\n"
        "✨ <b>Features:</b>\n"
        "  ┣━ Protect from copyright strikes\n"
        "  ┣━ Prevent fake reports\n"
        "  ┣━ Monitor suspicious activity\n"
        "  ┗━ Secure your content 24/7\n\n"
        "🔰 Tap /start to use me and save your channel from being banned! 🚀\n\n"
        "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛"
    )

    # Create buttons as specified
    keyboard = [
        [InlineKeyboardButton("🛡️ Protect Your Channel", url=ADD_CHANNEL_URL)],
        [InlineKeyboardButton("😍 Auto Reactions Bot 😍", url="https://t.me/auto_reaction0_bot")],
        [InlineKeyboardButton("☢️ Auto Join Acceptor ☢️", url="https://t.me/auto_join_request_acceptt_bot")],
        [InlineKeyboardButton("😎 More Cool Bots 😎", url="https://t.me/+f4_nu_cv9qY3MjZl")]
    ]
    markup = InlineKeyboardMarkup(keyboard)

    # Do not approve the join request, only message the user
    logger.info(f"Not approving join request for user {user.id} to chat {chat.id} as per configuration")

    # Send the welcome message
    try:
        # Try to send with a photo first
        photo_path = get_photo_path("wel.jpg")  # Using the welcome photo
        logger.info(f"Attempting to send welcome message with photo to user {user.id}")

        if photo_path:
            with open(photo_path, "rb") as photo:
                await context.bot.send_photo(
                    chat_id=user.id,
                    photo=photo,
                    caption=welcome_text,
                    parse_mode=ParseMode.HTML,
                    reply_markup=markup
                )
                logger.info(f"Successfully sent photo welcome message to user {user.id}")
                # Print to terminal that welcome message was sent successfully
                print(f"✅ Welcome message successfully sent to user {user.id} (@{user.username or 'no username'}) from channel {chat.title} (ID: {chat.id})")

                # Store the user's data in the database only after successful message delivery
                add_user(user.id, user.username)
                logger.info(f"User {user.id} added to database after successful welcome message")
        else:
            # Fallback to text if photo not available
            logger.warning(f"Welcome photo not found, sending text-only message to user {user.id}")
            await context.bot.send_message(
                chat_id=user.id,
                text=welcome_text,
                parse_mode=ParseMode.HTML,
                reply_markup=markup
            )
            logger.info(f"Successfully sent text-only welcome message to user {user.id}")
            # Print to terminal that welcome message was sent successfully
            print(f"✅ Welcome message (text-only) successfully sent to user {user.id} (@{user.username or 'no username'}) from channel {chat.title} (ID: {chat.id})")

            # Store the user's data in the database only after successful message delivery
            add_user(user.id, user.username)
            logger.info(f"User {user.id} added to database after successful text welcome message")
    except Forbidden as e:
        # Silently ignore "User hasn't started the bot" errors
        # No logging, no admin notification for these common errors
        logger.info(f"User {user.id} has not started the bot, skipping database storage")
        pass

    except Exception as e:
        # Check if this is a "user hasn't started the bot" error
        error_msg = str(e).lower()
        if "user not found" in error_msg or "chat not found" in error_msg or "user is deactivated" in error_msg or "bot was blocked" in error_msg:
            # Silently ignore these common errors
            logger.info(f"Cannot send message to user {user.id}, skipping database storage")
            pass
        else:
            # Log other errors in detail
            logger.error(f"Failed to send welcome message to user {user.id}: {e}", exc_info=True)

            # Only notify admin about non-standard errors
            try:
                admin_chat_id = ADMIN_CHAT_ID
                if admin_chat_id:
                    await context.bot.send_message(
                        chat_id=admin_chat_id,
                        text=f"❌ Error sending welcome message to user {user.id} (@{user.username or 'no username'}): {str(e)}"
                    )
            except Exception as admin_err:
                logger.error(f"Failed to notify admin about error: {admin_err}")

async def remove_channel_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Callback handler for removing a channel from the user's list.
    Format: remove_channel_{channel_id}
    """
    query = update.callback_query
    await query.answer()
    user = query.from_user

    try:
        # Extract channel_id from callback data
        callback_data = query.data
        if callback_data.startswith("remove_channel_"):
            channel_id = int(callback_data.split("_")[-1])

            # Check if channel belongs to user
            channel = channels_collection.find_one({"channel_id": channel_id, "admin_id": user.id})
            if not channel:
                await query.message.reply_text(
                    "❌ Channel not found or you don't have permission to remove it.",
                    parse_mode=ParseMode.HTML
                )
                return

            # Remove channel from database
            if remove_channel(channel_id):
                # Show confirmation message
                await query.message.reply_text(
                    f"✅ Channel <b>{channel.get('channel_name', 'Unknown')}</b> has been removed from your list.",
                    parse_mode=ParseMode.HTML
                )

                # Update the channel list display
                await refresh_mychannel_callback(update, context)
            else:
                await query.message.reply_text(
                    "❌ Failed to remove channel. Please try again.",
                    parse_mode=ParseMode.HTML
                )
    except Exception as e:
        logger.error(f"Error in remove_channel_callback: {e}")
        await query.message.reply_text(
            "❌ There was an error removing the channel. Please try again later.",
            parse_mode=ParseMode.HTML
        )

async def scan_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    /scan command: Automatically scan all user channels.
    Free users: 1 scan per day
    Premium users: 10 scans per day
    """
    user_id = update.effective_user.id
    user = update.effective_user

    # Check if user can scan
    can_scan, reason = can_user_scan(user_id)

    if not can_scan:
        if reason == "free_limit":
            text = (
                "┏━━━━━ ⚠️ 𝗦𝗖𝗔𝗡 𝗟𝗜𝗠𝗜𝗧 𝗥𝗘𝗔𝗖𝗛𝗘𝗗 ⚠️ ━━━━━┓\n\n"
                "🔒 <b>Daily scan limit reached!</b>\n\n"
                "┃ <b>Free Plan Limits:</b>\n"
                "┃ ┣━ 1 scan per day\n"
                "┃ ┗━ Resets at 00:00 UTC\n\n"
                "┏━━━━━ 💎 𝗨𝗣𝗚𝗥𝗔𝗗𝗘 𝗧𝗢 𝗣𝗥𝗘𝗠𝗜𝗨𝗠 💎 ━━━━━┓\n"
                "┃ ✅ 10 scans per day\n"
                "┃ ✅ Advanced protection\n"
                "┃ ✅ Priority support\n"
                "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛"
            )
            keyboard = [
                [InlineKeyboardButton("💎 Upgrade to Premium", callback_data="premium")]
            ]
        elif reason == "premium_limit":
            text = (
                "┏━━━━━ ⚠️ 𝗦𝗖𝗔𝗡 𝗟𝗜𝗠𝗜𝗧 𝗥𝗘𝗔𝗖𝗛𝗘𝗗 ⚠️ ━━━━━┓\n\n"
                "🔒 <b>Daily scan limit reached!</b>\n\n"
                "┃ <b>Premium Plan Limits:</b>\n"
                "┃ ┣━ 10 scans per day\n"
                "┃ ┗━ Resets at 00:00 UTC\n\n"
                "⏰ <i>Please try again tomorrow!</i>"
            )
            keyboard = []
        else:
            text = "❌ <b>Error:</b> Unable to perform scan at this time."
            keyboard = []

        markup = InlineKeyboardMarkup(keyboard) if keyboard else None
        await update.message.reply_text(text, parse_mode=ParseMode.HTML, reply_markup=markup)
        return

    # Get user's channels for scanning
    user_channels = get_user_channels(user_id, only_visible=True)

    if not user_channels:
        text = (
            "┏━━━━━ 🔍 𝗖𝗛𝗔𝗡𝗡𝗘𝗟 𝗦𝗖𝗔𝗡 🔍 ━━━━━┓\n\n"
            "❌ <b>No channels found!</b>\n\n"
            "┃ Please add channels first using /mychannel\n"
            "┃ Then return here to scan them for issues\n\n"
            "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛"
        )
        keyboard = [
            [InlineKeyboardButton("📊 Add Channels", callback_data="mychannel")]
        ]
        markup = InlineKeyboardMarkup(keyboard)
        await update.message.reply_text(text, parse_mode=ParseMode.HTML, reply_markup=markup)
        return

    # Record the scan
    record_scan(user_id)

    # Start scanning all channels automatically
    await scan_all_channels(update, context, user_id, user_channels)

async def scan_all_channels(update: Update, context: ContextTypes.DEFAULT_TYPE, user_id: int, user_channels: list):
    """
    Scan all channels for a user and display comprehensive results.
    """
    import asyncio

    # Get user info for scan logic
    is_premium = is_premium_user(user_id)
    channel_count = len(user_channels)

    # Show initial scanning message
    total_channels = len(user_channels)

    def create_progress_bar(percentage):
        """Create a visual progress bar"""
        filled = int(percentage / 10)  # Each block represents 10%
        empty = 10 - filled
        return "█" * filled + "░" * empty

    def get_scanning_message(stage, percentage, current_task):
        """Generate scanning message for each stage"""
        progress_bar = create_progress_bar(percentage)
        return (
            f"🔍 <b>Scanning {total_channels} Channel{'s' if total_channels != 1 else ''}...</b>\n\n"
            "┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n"
            f"┃ Progress: [{progress_bar}] {percentage}%     ┃\n"
            "┃                                     ┃\n"
            f"┃ {current_task:<35} ┃\n"
            "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n\n"
            f"⏳ <i>Stage {stage}/4: Deep scanning in progress...</i>"
        )

    # Send initial message (Stage 0)
    initial_text = get_scanning_message(1, 0, "🔄 Initializing scan engines...")
    if hasattr(update, 'message') and update.message:
        message = await update.message.reply_text(initial_text, parse_mode=ParseMode.HTML)
    else:
        # This is a callback query
        query = update.callback_query
        await query.answer()
        message = await query.edit_message_text(initial_text, parse_mode=ParseMode.HTML)

    # Progressive scanning with 4 stages (30 seconds total)
    # Stage 1: 10% progress (after 3 seconds)
    await asyncio.sleep(3)
    stage1_text = get_scanning_message(1, 10, "🔍 Analyzing channel content...")
    await message.edit_text(stage1_text, parse_mode=ParseMode.HTML)

    # Stage 2: 45% progress (after 12 seconds total)
    await asyncio.sleep(9)  # 9 more seconds (3 + 9 = 12)
    stage2_text = get_scanning_message(2, 45, "🛡️ Checking security violations...")
    await message.edit_text(stage2_text, parse_mode=ParseMode.HTML)

    # Stage 3: 86% progress (after 24 seconds total)
    await asyncio.sleep(12)  # 12 more seconds (12 + 12 = 24)
    stage3_text = get_scanning_message(3, 86, "📊 Generating comprehensive report...")
    await message.edit_text(stage3_text, parse_mode=ParseMode.HTML)

    # Stage 4: 100% completion (after 30 seconds total)
    await asyncio.sleep(6)  # 6 more seconds (24 + 6 = 30)
    final_text = get_scanning_message(4, 100, "✅ Finalizing scan results...")
    await message.edit_text(final_text, parse_mode=ParseMode.HTML)

    # Brief pause before showing results
    await asyncio.sleep(1)

    # Determine scan results based on business logic
    if is_premium:
        # Premium users always get "no issues" regardless of channel count
        issues_found = []
        scan_result = "clean"
    else:
        # Free users
        if channel_count <= 2:
            # Free users with ≤2 channels get "no issues"
            issues_found = []
            scan_result = "clean"
        else:
            # Free users with >2 channels get fake issues to promote premium
            issues_found = [
                "⚠️ Potential copyright violation detected",
                "🔍 Suspicious activity patterns found",
                "📊 Channel security could be improved",
                "🚨 Unauthorized access attempts detected"
            ]
            scan_result = "issues"

    # Generate results message
    if scan_result == "clean":
        result_text = (
            f"✅ <b>Scan Complete: All {total_channels} Channel{'s' if total_channels != 1 else ''}</b>\n\n"
            "┏━━━━━ 🛡️ 𝗦𝗖𝗔𝗡 𝗥𝗘𝗦𝗨𝗟𝗧𝗦 🛡️ ━━━━━┓\n\n"
            "🎉 <b>Excellent! No issues found across all channels.</b>\n\n"
            "┃ ✅ Copyright compliance: CLEAN\n"
            "┃ ✅ Security status: SECURE\n"
            "┃ ✅ Content analysis: SAFE\n"
            "┃ ✅ Protection level: OPTIMAL\n"
            f"┃ ✅ Channels scanned: {total_channels}\n\n"
            "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n\n"
            "🛡️ <i>All your channels are well protected!</i>"
        )
        keyboard = [
            [InlineKeyboardButton("🔍 Scan Again", callback_data="scan_menu")],
            [InlineKeyboardButton("📊 My Channels", callback_data="mychannel")]
        ]
    else:
        issues_text = "\n".join([f"┃ {issue}" for issue in issues_found])
        result_text = (
            f"⚠️ <b>Scan Complete: {total_channels} Channel{'s' if total_channels != 1 else ''} Scanned</b>\n\n"
            "┏━━━━━ 🔍 𝗦𝗖𝗔𝗡 𝗥𝗘𝗦𝗨𝗟𝗧𝗦 🔍 ━━━━━┓\n\n"
            "🚨 <b>Issues detected that need attention:</b>\n\n"
            f"{issues_text}\n\n"
            f"┃ 📊 Channels affected: {total_channels}\n"
            "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n\n"
            "💎 <b>Premium Protection Available:</b>\n"
            "┃ ✅ Advanced threat detection\n"
            "┃ ✅ Real-time monitoring\n"
            "┃ ✅ Automatic issue resolution\n"
            "┃ ✅ Priority support\n\n"
            "🛡️ <i>Upgrade to fix these issues across all channels!</i>"
        )
        keyboard = [
            [InlineKeyboardButton("💎 Upgrade to Premium", callback_data="premium")],
            [InlineKeyboardButton("🔍 Scan Again", callback_data="scan_menu")],
            [InlineKeyboardButton("📊 My Channels", callback_data="mychannel")]
        ]

    markup = InlineKeyboardMarkup(keyboard)
    await message.edit_text(result_text, parse_mode=ParseMode.HTML, reply_markup=markup)

async def scan_menu_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle scan menu callback - restart the scan process"""
    query = update.callback_query
    await query.answer()

    user_id = update.effective_user.id

    # Check if user can still scan
    can_scan, reason = can_user_scan(user_id)
    if not can_scan:
        if reason == "free_limit":
            text = "❌ <b>Daily scan limit reached!</b>\n\n💎 Upgrade to Premium for more scans."
        elif reason == "premium_limit":
            text = "❌ <b>Daily scan limit reached!</b>\n\n⏰ Try again tomorrow."
        else:
            text = "❌ <b>Unable to scan at this time.</b>"

        await query.edit_message_text(text, parse_mode=ParseMode.HTML)
        return

    # Get user's channels
    user_channels = get_user_channels(user_id, only_visible=True)

    if not user_channels:
        text = (
            "┏━━━━━ 🔍 𝗖𝗛𝗔𝗡𝗡𝗘𝗟 𝗦𝗖𝗔𝗡 🔍 ━━━━━┓\n\n"
            "❌ <b>No channels found!</b>\n\n"
            "┃ Please add channels first using /mychannel\n"
            "┃ Then return here to scan them for issues\n\n"
            "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛"
        )
        keyboard = [
            [InlineKeyboardButton("📊 Add Channels", callback_data="mychannel")]
        ]
        markup = InlineKeyboardMarkup(keyboard)
        await query.edit_message_text(text, parse_mode=ParseMode.HTML, reply_markup=markup)
        return

    # Record the scan
    record_scan(user_id)

    # Start scanning all channels
    await scan_all_channels(update, context, user_id, user_channels)

async def scan_channel_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle channel scan button clicks"""
    query = update.callback_query
    await query.answer()

    user_id = update.effective_user.id

    # Extract channel_id from callback data
    callback_data = query.data
    if not callback_data.startswith("scan_channel_"):
        await query.edit_message_text("❌ Invalid scan request.")
        return

    try:
        channel_id = int(callback_data.replace("scan_channel_", ""))
    except ValueError:
        await query.edit_message_text("❌ Invalid channel ID.")
        return

    # Check if user can still scan
    can_scan, reason = can_user_scan(user_id)
    if not can_scan:
        if reason == "free_limit":
            text = "❌ <b>Daily scan limit reached!</b>\n\n💎 Upgrade to Premium for more scans."
        elif reason == "premium_limit":
            text = "❌ <b>Daily scan limit reached!</b>\n\n⏰ Try again tomorrow."
        else:
            text = "❌ <b>Unable to scan at this time.</b>"

        await query.edit_message_text(text, parse_mode=ParseMode.HTML)
        return

    # Get channel info
    channel_info = get_channel(channel_id)
    if not channel_info or channel_info.get('admin_id') != user_id:
        await query.edit_message_text("❌ Channel not found or access denied.")
        return

    channel_name = channel_info.get('channel_name', 'Unknown Channel')

    # Record the scan
    record_scan(user_id)

    # Perform fake scan based on user type
    is_premium = is_premium_user(user_id)
    user_channels = get_user_channels(user_id, only_visible=True)
    channel_count = len(user_channels)

    # Determine scan results based on business logic
    if is_premium:
        # Premium users always get "no issues" regardless of channel count
        issues_found = []
        scan_result = "clean"
    else:
        # Free users
        if channel_count <= 2:
            # Free users with ≤2 channels get "no issues"
            issues_found = []
            scan_result = "clean"
        else:
            # Free users with >2 channels get fake issues to promote premium
            issues_found = [
                "⚠️ Potential copyright violation detected",
                "🔍 Suspicious activity patterns found",
                "📊 Channel security could be improved"
            ]
            scan_result = "issues"

    # Show scanning animation first
    scanning_text = (
        f"🔍 <b>Scanning {channel_name}...</b>\n\n"
        "┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n"
        "┃ 🔄 Analyzing channel content...      ┃\n"
        "┃ 🔍 Checking for violations...        ┃\n"
        "┃ 🛡️ Reviewing security status...      ┃\n"
        "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n\n"
        "⏳ <i>Please wait...</i>"
    )

    await query.edit_message_text(scanning_text, parse_mode=ParseMode.HTML)

    # Wait a bit to simulate scanning
    import asyncio
    await asyncio.sleep(3)

    # Show results
    if scan_result == "clean":
        result_text = (
            f"✅ <b>Scan Complete: {channel_name}</b>\n\n"
            "┏━━━━━ 🛡️ 𝗦𝗖𝗔𝗡 𝗥𝗘𝗦𝗨𝗟𝗧𝗦 🛡️ ━━━━━┓\n\n"
            "🎉 <b>Great news! No issues found.</b>\n\n"
            "┃ ✅ Copyright compliance: CLEAN\n"
            "┃ ✅ Security status: SECURE\n"
            "┃ ✅ Content analysis: SAFE\n"
            "┃ ✅ Protection level: OPTIMAL\n\n"
            "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n\n"
            "🛡️ <i>Your channel is well protected!</i>"
        )
        keyboard = [
            [InlineKeyboardButton("🔍 Scan Another Channel", callback_data="scan_menu")],
            [InlineKeyboardButton("📊 My Channels", callback_data="mychannel")]
        ]
    else:
        issues_text = "\n".join([f"┃ {issue}" for issue in issues_found])
        result_text = (
            f"⚠️ <b>Scan Complete: {channel_name}</b>\n\n"
            "┏━━━━━ 🔍 𝗦𝗖𝗔𝗡 𝗥𝗘𝗦𝗨𝗟𝗧𝗦 🔍 ━━━━━┓\n\n"
            "🚨 <b>Issues detected that need attention:</b>\n\n"
            f"{issues_text}\n\n"
            "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n\n"
            "💎 <b>Premium Protection Available:</b>\n"
            "┃ ✅ Advanced threat detection\n"
            "┃ ✅ Real-time monitoring\n"
            "┃ ✅ Automatic issue resolution\n"
            "┃ ✅ Priority support\n\n"
            "🛡️ <i>Upgrade to fix these issues!</i>"
        )
        keyboard = [
            [InlineKeyboardButton("💎 Upgrade to Premium", callback_data="premium")],
            [InlineKeyboardButton("🔍 Scan Another Channel", callback_data="scan_menu")],
            [InlineKeyboardButton("📊 My Channels", callback_data="mychannel")]
        ]

    markup = InlineKeyboardMarkup(keyboard)
    await query.edit_message_text(result_text, parse_mode=ParseMode.HTML, reply_markup=markup)

async def stats_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Shows statistics about the bot usage.
    Different view for admins and regular users.
    """
    user_id = update.effective_user.id

    # Check if user is admin
    is_admin = (ADMIN_CHAT_ID and user_id == int(ADMIN_CHAT_ID))

    try:
        # Get actual stats from database
        from database import users_collection
        actual_users = users_collection.count_documents({})
        actual_channels = channels_collection.count_documents({})
        premium_count = 0

        # Try to count premium users if the field exists
        try:
            premium_count = users_collection.count_documents({"is_premium": True})
        except Exception:
            # If field doesn't exist or other error, just use 0
            pass

        if is_admin:
            # Admin sees actual numbers and more detailed stats
            today = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
            yesterday = today - timedelta(days=1)

            # Try to get today's and yesterday's stats
            try:
                users_today = users_collection.count_documents({
                    "created_at": {"$gte": today}
                })

                users_yesterday = users_collection.count_documents({
                    "created_at": {"$gte": yesterday, "$lt": today}
                })

                channels_today = channels_collection.count_documents({
                    "created_at": {"$gte": today}
                })

                channels_yesterday = channels_collection.count_documents({
                    "created_at": {"$gte": yesterday, "$lt": today}
                })
            except Exception:
                # If date fields don't exist, use defaults
                users_today = users_yesterday = channels_today = channels_yesterday = 0

            stats_text = (
                "┏━━━━━ 📊 𝗔𝗗𝗠𝗜𝗡 𝗦𝗧𝗔𝗧𝗜𝗦𝗧𝗜𝗖𝗦 📊 ━━━━━┓\n\n"
                f"👥 <b>Total Users:</b> {actual_users:,}\n"
                f"🟢 <b>Users Today:</b> {users_today:,}\n"
                f"🟡 <b>Users Yesterday:</b> {users_yesterday:,}\n\n"
                f"📢 <b>Total Channels:</b> {actual_channels:,}\n"
                f"🟢 <b>Channels Today:</b> {channels_today:,}\n"
                f"🟡 <b>Channels Yesterday:</b> {channels_yesterday:,}\n\n"
                f"💎 <b>Premium Users:</b> {premium_count:,}\n"
                f"📅 <b>Generated:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛"
            )

            # No buttons for admin view
            await update.message.reply_text(stats_text, parse_mode=ParseMode.HTML)
            return
        else:
            # Regular user sees enhanced numbers
            # Get user's channels count
            user_channels = len(get_user_channels(user_id, only_visible=True))

            # Enhanced stats for display (actual + marketing buffer)
            display_users = actual_users + 3000  # Add 3000 to actual user count
            display_channels = actual_channels + 17000  # Add 17000 to actual channel count

            # Random daily growth number between 50-150
            import random
            daily_growth = random.randint(50, 150)

            # Calculate a fake "uptime" percentage between 99.7% and 99.99%
            uptime = 99.7 + random.random() * 0.29

            # Build the stats message with creative UI
            stats_text = (
                "┏━━━━━ 🚀 𝗚𝗟𝗢𝗕𝗔𝗟 𝗣𝗥𝗢𝗧𝗘𝗖𝗧𝗜𝗢𝗡 𝗦𝗧𝗔𝗧𝗦 🚀 ━━━━━┓\n\n"
                f"👥 <b>Community Members:</b> {display_users:,}+\n"
                f"🛡️ <b>Protected Channels:</b> {display_channels:,}+\n"
                f"⚡ <b>System Uptime:</b> {uptime:.2f}%\n\n"
                "┏━━━━━ 🔐 𝗣𝗥𝗢𝗧𝗘𝗖𝗧𝗜𝗢𝗡 𝗣𝗘𝗥𝗙𝗢𝗥𝗠𝗔𝗡𝗖𝗘 🔐 ━━━━━┓\n"
                "┃ 💯 Copyright Reports Blocked: 32,849+\n"
                "┃ 🔄 Messages Processed: 280M+\n"
                "┃ 🎯 Protection Accuracy: 92.7%\n"
                "┃ 🔒 Available Servers: 24\n"
                "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n\n"
                f"<b>Your Channels:</b> {user_channels} " + ("protected" if user_channels > 0 else "- Add your first channel now!") + "\n\n"
                "💎 <i>Join premium today to unlock enhanced protection!</i>"
            )

            # Add premium button for non-premium users
            if not is_premium_user(user_id):
                keyboard = [
                    [InlineKeyboardButton("💎 Upgrade to Premium", callback_data="premium")],
                    [InlineKeyboardButton("📊 My Channels", callback_data="mychannel")]
                ]
                markup = InlineKeyboardMarkup(keyboard)
            else:
                keyboard = [
                    [InlineKeyboardButton("📊 My Channels", callback_data="mychannel")]
                ]
                markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(stats_text, reply_markup=markup, parse_mode=ParseMode.HTML)
            return

    except Exception as e:
        logger.error(f"Error generating stats: {e}")
        stats_text = "❌ Error generating statistics. Please try again later."
        await update.message.reply_text(stats_text, parse_mode=ParseMode.HTML)

def main():
    """Start the bot."""
    # Create the Application and pass it your bot's token
    application = Application.builder().token(TOKEN).build()

    # Add command handlers
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("help", help_command))
    application.add_handler(CommandHandler("mychannel", mychannel_command))
    application.add_handler(CommandHandler("premium", premium_command))
    application.add_handler(CommandHandler("stats", stats_command))

    # Add callback query handlers
    application.add_handler(CallbackQueryHandler(refresh_mychannel_callback, pattern="^refresh_channels$"))
    application.add_handler(CallbackQueryHandler(premium_callback, pattern="^premium$"))
    application.add_handler(CallbackQueryHandler(choose_payment_callback, pattern="^choose_payment$"))
    application.add_handler(CallbackQueryHandler(show_payment_methods_callback, pattern="^show_payment_methods$"))
    application.add_handler(CallbackQueryHandler(crypto_payment_callback, pattern="^crypto_payment$"))
    application.add_handler(CallbackQueryHandler(start_process_callback, pattern="^start_process$"))
    application.add_handler(CallbackQueryHandler(i_have_joined_callback, pattern="^i_have_joined$"))
    application.add_handler(CallbackQueryHandler(help_command, pattern="^help_command$"))
    application.add_handler(CallbackQueryHandler(check_joined_callback, pattern="^check_joined$"))
    application.add_handler(CallbackQueryHandler(mychannel_callback, pattern="^mychannel$"))

    # Channel-related handlers
    application.add_handler(CallbackQueryHandler(remove_channel_callback, pattern="^remove_channel_"))
    application.add_handler(ChatJoinRequestHandler(join_request_handler))
    application.add_handler(ChatMemberHandler(handle_chat_member_update, ChatMemberHandler.MY_CHAT_MEMBER))

    # Start the Bot
    application.run_polling(allowed_updates=Update.ALL_TYPES)
